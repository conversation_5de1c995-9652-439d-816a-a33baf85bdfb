-- =====================================================
-- GYM PROJECT - MASTER INDEX CREATION SCRIPT
-- =====================================================
-- Bu script tüm database indexlerini sıralı şekilde oluşturur
-- 1000+ salon, 100.000+ kullanıcı için optimize edilmiştir
-- 
-- ESTIMATED TOTAL TIME: 60-90 minutes
-- ESTIMATED RAM USAGE: ~3.3 GB
-- PERFORMANCE IMPROVEMENT: 80-95%
-- =====================================================

USE [GymProjectDatabase]
GO

-- =====================================================
-- PRE-EXECUTION CHECKS
-- =====================================================
PRINT '============================================='
PRINT 'GYM PROJECT - DATABASE INDEX CREATION'
PRINT '============================================='
PRINT 'Starting comprehensive index creation process...'
PRINT 'Target: 1000+ gyms, 100,000+ users'
PRINT 'Expected performance improvement: 80-95%'
PRINT '============================================='

-- Check database size and free space
DECLARE @DatabaseSize DECIMAL(10,2)
DECLARE @FreeSpace DECIMAL(10,2)

SELECT 
    @DatabaseSize = SUM(size * 8.0 / 1024) / 1024,
    @FreeSpace = SUM(CASE WHEN type = 0 THEN size * 8.0 / 1024 END) / 1024
FROM sys.database_files

PRINT 'Current database size: ' + CAST(@DatabaseSize AS VARCHAR(10)) + ' GB'
PRINT 'Estimated space needed for indexes: ~2 GB'
PRINT 'Please ensure sufficient disk space before proceeding.'
PRINT ''

-- Check current index count
DECLARE @CurrentIndexCount INT
SELECT @CurrentIndexCount = COUNT(*)
FROM sys.indexes 
WHERE object_id IN (SELECT object_id FROM sys.tables)
    AND is_primary_key = 0
    AND is_unique_constraint = 0

PRINT 'Current non-PK indexes: ' + CAST(@CurrentIndexCount AS VARCHAR(10))
PRINT 'Indexes to be created: ~200+'
PRINT ''

-- =====================================================
-- EXECUTION PLAN
-- =====================================================
PRINT 'EXECUTION PLAN:'
PRINT '1. Ultra High Priority Indexes (15-30 min) - Critical tables'
PRINT '2. High Priority Indexes (20-40 min) - Business logic tables'
PRINT '3. Medium Priority Indexes (15-25 min) - Module-specific tables'
PRINT '4. Low Priority Indexes (5-10 min) - Lookup tables'
PRINT '5. Maintenance procedures and views'
PRINT ''

-- =====================================================
-- SAFETY CHECKS
-- =====================================================
-- Check for existing critical indexes to avoid conflicts
IF EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_Members_CompanyID_PhoneNumber_IsActive')
BEGIN
    PRINT 'WARNING: Some critical indexes already exist!'
    PRINT 'This script may encounter conflicts.'
    PRINT 'Consider dropping existing custom indexes first.'
    PRINT ''
END

-- Check SQL Server version for Full-Text support
DECLARE @SQLVersion VARCHAR(50)
SELECT @SQLVersion = @@VERSION
PRINT 'SQL Server Version: ' + LEFT(@SQLVersion, 50) + '...'

IF CHARINDEX('Express', @SQLVersion) > 0
BEGIN
    PRINT 'WARNING: SQL Server Express detected!'
    PRINT 'Full-Text Search may not be available.'
    PRINT 'Some Full-Text indexes will be skipped.'
    PRINT ''
END

-- =====================================================
-- MEMORY ESTIMATION
-- =====================================================
PRINT 'MEMORY USAGE ESTIMATION:'
PRINT '- Ultra High Priority: ~800 MB'
PRINT '- High Priority: ~1.5 GB'
PRINT '- Medium Priority: ~800 MB'
PRINT '- Low Priority: ~200 MB'
PRINT '- TOTAL ESTIMATED: ~3.3 GB'
PRINT ''

-- Check available memory
DECLARE @TotalMemoryMB BIGINT
SELECT @TotalMemoryMB = total_physical_memory_kb / 1024
FROM sys.dm_os_sys_memory

PRINT 'Server total memory: ' + CAST(@TotalMemoryMB AS VARCHAR(10)) + ' MB'

IF @TotalMemoryMB < 8192
BEGIN
    PRINT 'WARNING: Server has less than 8 GB RAM!'
    PRINT 'Consider running scripts in smaller batches.'
    PRINT 'Monitor memory usage during execution.'
    PRINT ''
END

-- =====================================================
-- PERFORMANCE BASELINE
-- =====================================================
PRINT 'PERFORMANCE BASELINE:'
PRINT 'Recording current performance metrics...'

-- Create baseline table if not exists
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'IndexCreationBaseline')
BEGIN
    CREATE TABLE IndexCreationBaseline (
        RecordDate DATETIME,
        Phase VARCHAR(50),
        TotalIndexes INT,
        AvgQueryTime DECIMAL(10,2),
        Notes VARCHAR(500)
    )
END

-- Record baseline
INSERT INTO IndexCreationBaseline (RecordDate, Phase, TotalIndexes, Notes)
VALUES (GETDATE(), 'BEFORE_INDEX_CREATION', @CurrentIndexCount, 'Baseline before index creation')

PRINT 'Baseline recorded successfully.'
PRINT ''

-- =====================================================
-- EXECUTION CONFIRMATION
-- =====================================================
PRINT '============================================='
PRINT 'READY TO START INDEX CREATION'
PRINT '============================================='
PRINT 'This process will:'
PRINT '✓ Create ~200+ optimized indexes'
PRINT '✓ Improve query performance by 80-95%'
PRINT '✓ Use approximately 3.3 GB additional RAM'
PRINT '✓ Take 60-90 minutes to complete'
PRINT ''
PRINT 'The process is divided into 4 phases for safety.'
PRINT 'You can stop between phases if needed.'
PRINT ''
PRINT 'IMPORTANT: Monitor system resources during execution!'
PRINT '============================================='

-- Wait for user confirmation (comment out for automated execution)
-- PRINT 'Press any key to continue or Ctrl+C to cancel...'
-- WAITFOR DELAY '00:00:05'

PRINT 'Starting index creation process...'
PRINT ''

-- =====================================================
-- PHASE 1: ULTRA HIGH PRIORITY
-- =====================================================
PRINT '============================================='
PRINT 'PHASE 1: ULTRA HIGH PRIORITY INDEXES'
PRINT '============================================='
PRINT 'Creating indexes for critical system tables...'
PRINT 'Tables: Members, Memberships, Payments, Transactions, UserDevices, UserOperationClaims'
PRINT 'Estimated time: 15-30 minutes'
PRINT 'Estimated RAM: ~800 MB'
PRINT ''

-- Execute Phase 1
:r "01_CreateIndexes_UltraHighPriority.sql"

-- Record progress
INSERT INTO IndexCreationBaseline (RecordDate, Phase, TotalIndexes, Notes)
SELECT GETDATE(), 'PHASE_1_COMPLETED', COUNT(*), 'Ultra high priority indexes completed'
FROM sys.indexes 
WHERE object_id IN (SELECT object_id FROM sys.tables)
    AND is_primary_key = 0
    AND is_unique_constraint = 0

PRINT 'PHASE 1 COMPLETED SUCCESSFULLY!'
PRINT ''

-- =====================================================
-- PHASE 2: HIGH PRIORITY
-- =====================================================
PRINT '============================================='
PRINT 'PHASE 2: HIGH PRIORITY INDEXES'
PRINT '============================================='
PRINT 'Creating indexes for business logic tables...'
PRINT 'Tables: Companies, CompanyUsers, Users, OperationClaims, Expenses, etc.'
PRINT 'Estimated time: 20-40 minutes'
PRINT 'Estimated RAM: ~1.5 GB additional'
PRINT ''

-- Execute Phase 2
:r "02_CreateIndexes_HighPriority.sql"

-- Record progress
INSERT INTO IndexCreationBaseline (RecordDate, Phase, TotalIndexes, Notes)
SELECT GETDATE(), 'PHASE_2_COMPLETED', COUNT(*), 'High priority indexes completed'
FROM sys.indexes 
WHERE object_id IN (SELECT object_id FROM sys.tables)
    AND is_primary_key = 0
    AND is_unique_constraint = 0

PRINT 'PHASE 2 COMPLETED SUCCESSFULLY!'
PRINT ''

-- =====================================================
-- PHASE 3: MEDIUM PRIORITY
-- =====================================================
PRINT '============================================='
PRINT 'PHASE 3: MEDIUM PRIORITY INDEXES'
PRINT '============================================='
PRINT 'Creating indexes for module-specific tables...'
PRINT 'Tables: Products, WorkoutProgram tables, Exercise tables, etc.'
PRINT 'Estimated time: 15-25 minutes'
PRINT 'Estimated RAM: ~800 MB additional'
PRINT ''

-- Execute Phase 3
:r "03_CreateIndexes_MediumPriority.sql"

-- Record progress
INSERT INTO IndexCreationBaseline (RecordDate, Phase, TotalIndexes, Notes)
SELECT GETDATE(), 'PHASE_3_COMPLETED', COUNT(*), 'Medium priority indexes completed'
FROM sys.indexes 
WHERE object_id IN (SELECT object_id FROM sys.tables)
    AND is_primary_key = 0
    AND is_unique_constraint = 0

PRINT 'PHASE 3 COMPLETED SUCCESSFULLY!'
PRINT ''

-- =====================================================
-- PHASE 4: LOW PRIORITY & MAINTENANCE
-- =====================================================
PRINT '============================================='
PRINT 'PHASE 4: LOW PRIORITY & MAINTENANCE'
PRINT '============================================='
PRINT 'Creating indexes for lookup tables and maintenance procedures...'
PRINT 'Tables: Cities, Towns, LicensePackages, etc.'
PRINT 'Estimated time: 5-10 minutes'
PRINT 'Estimated RAM: ~200 MB additional'
PRINT ''

-- Execute Phase 4
:r "04_CreateIndexes_LowPriority.sql"

-- Record final progress
INSERT INTO IndexCreationBaseline (RecordDate, Phase, TotalIndexes, Notes)
SELECT GETDATE(), 'ALL_PHASES_COMPLETED', COUNT(*), 'All index creation phases completed successfully'
FROM sys.indexes 
WHERE object_id IN (SELECT object_id FROM sys.tables)
    AND is_primary_key = 0
    AND is_unique_constraint = 0

PRINT 'PHASE 4 COMPLETED SUCCESSFULLY!'
PRINT ''

-- =====================================================
-- FINAL SUMMARY
-- =====================================================
DECLARE @FinalIndexCount INT
SELECT @FinalIndexCount = COUNT(*)
FROM sys.indexes 
WHERE object_id IN (SELECT object_id FROM sys.tables)
    AND is_primary_key = 0
    AND is_unique_constraint = 0

DECLARE @NewIndexes INT = @FinalIndexCount - @CurrentIndexCount

PRINT '============================================='
PRINT 'INDEX CREATION PROCESS COMPLETED!'
PRINT '============================================='
PRINT 'Summary:'
PRINT '- Initial indexes: ' + CAST(@CurrentIndexCount AS VARCHAR(10))
PRINT '- Final indexes: ' + CAST(@FinalIndexCount AS VARCHAR(10))
PRINT '- New indexes created: ' + CAST(@NewIndexes AS VARCHAR(10))
PRINT '- Estimated RAM usage: ~3.3 GB'
PRINT '- Expected performance improvement: 80-95%'
PRINT ''
PRINT 'Next Steps:'
PRINT '1. Monitor application performance'
PRINT '2. Run sp_CheckIndexUsage to verify index usage'
PRINT '3. Set up weekly index maintenance jobs'
PRINT '4. Monitor memory usage and adjust if needed'
PRINT ''
PRINT 'Maintenance Commands:'
PRINT '- EXEC sp_CheckIndexFragmentation'
PRINT '- EXEC sp_CheckIndexUsage'
PRINT '- SELECT * FROM IndexCreationBaseline'
PRINT '============================================='
PRINT 'INDEX CREATION SUCCESSFULLY COMPLETED!'
PRINT '============================================='
GO
