-- =====================================================
-- GYM PROJECT - DATABASE INDEX CREATION SCRIPT
-- PART 1: ULTRA HIGH PRIORITY INDEXES
-- =====================================================
-- Bu script 1000+ salon, 100.000+ kullanıcı için optimize edilmiştir
-- Estimated RAM Usage: ~2-3 GB for all indexes
-- =====================================================

USE [GymProjectDatabase]
GO

PRINT 'Starting ULTRA HIGH PRIORITY index creation...'
PRINT 'Estimated completion time: 15-30 minutes'
GO

-- =====================================================
-- 1. MEMBERS TABLE (ULTRA CRITICAL - System Heart)
-- =====================================================
PRINT 'Creating Member indexes...'

-- Multi-tenant base query (En kritik)
CREATE INDEX IX_Members_CompanyID_IsActive_MemberID 
ON Members (CompanyID, IsActive, MemberID)
GO

-- Phone number lookup (Giriş sistemi - ULTRA KRİTİK)
CREATE UNIQUE INDEX IX_Members_CompanyID_PhoneNumber_IsActive 
ON Members (CompanyID, PhoneNumber, IsActive)
WHERE PhoneNumber IS NOT NULL
GO

-- QR code lookup (Giriş sistemi - ULTRA KRİTİK)
CREATE UNIQUE INDEX IX_Members_CompanyID_ScanNumber_IsActive 
ON Members (CompanyID, ScanNumber, IsActive)
WHERE ScanNumber IS NOT NULL
GO

-- Name search (Arama sistemi)
CREATE INDEX IX_Members_CompanyID_IsActive_Name 
ON Members (CompanyID, IsActive, Name)
GO

-- User integration
CREATE INDEX IX_Members_CompanyID_UserID_IsActive 
ON Members (CompanyID, UserID, IsActive)
WHERE UserID IS NOT NULL
GO

-- Balance queries (Bakiye filtreleme)
CREATE INDEX IX_Members_CompanyID_IsActive_Balance 
ON Members (CompanyID, IsActive, Balance)
GO

-- Covering index (JOIN performance)
CREATE INDEX IX_Members_CompanyID_MemberID_IsActive 
ON Members (CompanyID, MemberID, IsActive)
INCLUDE (Name, PhoneNumber, Gender, Email, Balance, ScanNumber, UserID, CreationDate, UpdatedDate)
GO

-- Full-text search
CREATE FULLTEXT INDEX FTI_Members_SearchFields
ON Members (Name, PhoneNumber, Email, Adress)
GO

-- =====================================================
-- 2. MEMBERSHIPS TABLE (ULTRA CRITICAL - System Heart)
-- =====================================================
PRINT 'Creating Membership indexes...'

-- Multi-tenant + Member relationship (ULTRA KRİTİK)
CREATE INDEX IX_Memberships_CompanyID_MemberID_IsActive 
ON Memberships (CompanyID, MemberID, IsActive)
GO

-- Active membership check (ULTRA KRİTİK)
CREATE INDEX IX_Memberships_CompanyID_IsActive_EndDate_IsFrozen 
ON Memberships (CompanyID, IsActive, EndDate, IsFrozen)
GO

-- MembershipType relationship (JOIN performance)
CREATE INDEX IX_Memberships_CompanyID_MembershipTypeID_IsActive 
ON Memberships (CompanyID, MembershipTypeID, IsActive)
GO

-- EndDate queries (Remaining days)
CREATE INDEX IX_Memberships_CompanyID_IsActive_EndDate 
ON Memberships (CompanyID, IsActive, EndDate)
GO

-- Duplicate check (Business validation)
CREATE INDEX IX_Memberships_MemberID_MembershipTypeID_EndDate_IsActive 
ON Memberships (MemberID, MembershipTypeID, EndDate, IsActive)
GO

-- Covering index (JOIN performance)
CREATE INDEX IX_Memberships_CompanyID_MemberID_MembershipTypeID 
ON Memberships (CompanyID, MemberID, MembershipTypeID)
INCLUDE (MembershipID, StartDate, EndDate, IsActive, IsFrozen, FreezeStartDate, FreezeEndDate, CreationDate, UpdatedDate)
GO

-- =====================================================
-- 3. PAYMENTS TABLE (ULTRA CRITICAL - Financial Heart)
-- =====================================================
PRINT 'Creating Payment indexes...'

-- Multi-tenant + Membership relationship (ULTRA KRİTİK)
CREATE INDEX IX_Payments_CompanyID_MemberShipID_IsActive 
ON Payments (CompanyID, MemberShipID, IsActive)
GO

-- Date range queries (Financial reports - ULTRA KRİTİK)
CREATE INDEX IX_Payments_CompanyID_IsActive_PaymentDate 
ON Payments (CompanyID, IsActive, PaymentDate)
GO

-- Payment method analytics
CREATE INDEX IX_Payments_CompanyID_IsActive_OriginalPaymentMethod 
ON Payments (CompanyID, IsActive, OriginalPaymentMethod)
WHERE OriginalPaymentMethod IS NOT NULL
GO

-- Monthly revenue optimization
CREATE INDEX IX_Payments_CompanyID_PaymentDate_Year_Month 
ON Payments (CompanyID, PaymentDate)
WHERE IsActive = 1
INCLUDE (PaymentAmount, OriginalPaymentMethod)
GO

-- Covering index (JOIN performance)
CREATE INDEX IX_Payments_CompanyID_MemberShipID_PaymentDate 
ON Payments (CompanyID, MemberShipID, PaymentDate)
INCLUDE (PaymentID, PaymentAmount, PaymentMethod, OriginalPaymentMethod, FinalPaymentMethod, PaymentStatus, IsActive, CreationDate, UpdatedDate)
GO

-- =====================================================
-- 4. TRANSACTIONS TABLE (ULTRA CRITICAL - Financial)
-- =====================================================
PRINT 'Creating Transaction indexes...'

-- Multi-tenant + Member relationship (ULTRA KRİTİK)
CREATE INDEX IX_Transactions_CompanyID_MemberID_IsActive 
ON Transactions (CompanyID, MemberID, IsActive)
GO

-- Unpaid transactions (ULTRA KRİTİK)
CREATE INDEX IX_Transactions_CompanyID_MemberID_IsPaid_IsActive 
ON Transactions (CompanyID, MemberID, IsPaid, IsActive)
GO

-- Date range analytics (ULTRA KRİTİK)
CREATE INDEX IX_Transactions_CompanyID_IsActive_TransactionDate 
ON Transactions (CompanyID, IsActive, TransactionDate)
GO

-- Monthly analytics optimization
CREATE INDEX IX_Transactions_CompanyID_TransactionDate_Year_Month 
ON Transactions (CompanyID, TransactionDate)
WHERE IsActive = 1 AND TransactionType != 'Bakiye Yükleme'
INCLUDE (UnitPrice, Quantity, Amount)
GO

-- Covering index (Analytics performance)
CREATE INDEX IX_Transactions_CompanyID_MemberID_ProductID 
ON Transactions (CompanyID, MemberID, ProductID)
INCLUDE (TransactionID, Amount, UnitPrice, TransactionType, TransactionDate, Quantity, IsPaid, IsActive, CreationDate, UpdatedDate)
GO

-- =====================================================
-- 5. USER DEVICES TABLE (ULTRA CRITICAL - Authentication)
-- =====================================================
PRINT 'Creating UserDevice indexes...'

-- Refresh token lookup (ULTRA KRİTİK - Authentication)
CREATE UNIQUE INDEX IX_UserDevices_RefreshToken_IsActive 
ON UserDevices (RefreshToken, IsActive)
WHERE RefreshToken IS NOT NULL AND IsActive = 1
GO

-- User device management (ULTRA KRİTİK)
CREATE INDEX IX_UserDevices_UserId_IsActive 
ON UserDevices (UserId, IsActive)
GO

-- Token expiration cleanup (Scheduled job)
CREATE INDEX IX_UserDevices_IsActive_RefreshTokenExpiration 
ON UserDevices (IsActive, RefreshTokenExpiration)
WHERE RefreshTokenExpiration IS NOT NULL
GO

-- Covering index (Device management)
CREATE INDEX IX_UserDevices_UserId_IsActive_CreatedAt 
ON UserDevices (UserId, IsActive, CreatedAt)
INCLUDE (Id, RefreshToken, RefreshTokenExpiration, DeviceInfo, LastIpAddress, LastUsedAt)
GO

-- =====================================================
-- 6. USER OPERATION CLAIMS TABLE (ULTRA CRITICAL - Authorization)
-- =====================================================
PRINT 'Creating UserOperationClaim indexes...'

-- User claims lookup (ULTRA KRİTİK - Authorization)
CREATE INDEX IX_UserOperationClaims_UserId_IsActive 
ON UserOperationClaims (UserId, IsActive)
GO

-- Role-based queries (ULTRA KRİTİK)
CREATE INDEX IX_UserOperationClaims_OperationClaimId_IsActive 
ON UserOperationClaims (OperationClaimId, IsActive)
GO

-- User-Role unique relationship
CREATE UNIQUE INDEX IX_UserOperationClaims_UserId_OperationClaimId_IsActive 
ON UserOperationClaims (UserId, OperationClaimId, IsActive)
WHERE IsActive = 1
GO

-- Covering index (JOIN performance)
CREATE INDEX IX_UserOperationClaims_UserId_OperationClaimId 
ON UserOperationClaims (UserId, OperationClaimId)
INCLUDE (UserOperationClaimId, IsActive, CreationDate)
GO

PRINT 'ULTRA HIGH PRIORITY indexes completed successfully!'
PRINT 'Estimated RAM usage so far: ~800 MB'
GO
