-- =====================================================
-- GYM PROJECT - DATABASE INDEX CREATION SCRIPT
-- PART 2: HIGH PRIORITY INDEXES
-- =====================================================
-- Bu script kritik business operations için gerekli indexleri oluşturur
-- Estimated RAM Usage: ~1.5 GB additional
-- =====================================================

USE [GymProjectDatabase]
GO

PRINT 'Starting HIGH PRIORITY index creation...'
PRINT 'Estimated completion time: 20-40 minutes'
GO

-- =====================================================
-- 7. COMPANIES TABLE (HIGH PRIORITY - Multi-tenant Core)
-- =====================================================
PRINT 'Creating Company indexes...'

-- Active companies
CREATE INDEX IX_Companies_IsActive_CompanyName 
ON Companies (IsActive, CompanyName)
GO

-- Company name lookup
CREATE INDEX IX_Companies_CompanyName 
ON Companies (CompanyName)
GO

-- Covering index
CREATE INDEX IX_Companies_CompanyID_IsActive 
ON Companies (CompanyID, IsActive)
INCLUDE (CompanyName, PhoneNumber, CreationDate, UpdatedDate)
GO

-- =====================================================
-- 8. COMPANY USERS TABLE (HIGH PRIORITY - User Management)
-- =====================================================
PRINT 'Creating CompanyUser indexes...'

-- Multi-tenant base query
CREATE INDEX IX_CompanyUsers_CompanyID_IsActive_CompanyUserID 
ON CompanyUsers (CompanyID, IsActive, CompanyUserID)
GO

-- Email lookup (ULTRA KRİTİK)
CREATE UNIQUE INDEX IX_CompanyUsers_CompanyID_Email_IsActive 
ON CompanyUsers (CompanyID, Email, IsActive)
WHERE Email IS NOT NULL
GO

-- Phone lookup
CREATE INDEX IX_CompanyUsers_CompanyID_PhoneNumber_IsActive 
ON CompanyUsers (CompanyID, PhoneNumber, IsActive)
WHERE PhoneNumber IS NOT NULL
GO

-- Name search
CREATE INDEX IX_CompanyUsers_CompanyID_IsActive_Name 
ON CompanyUsers (CompanyID, IsActive, Name)
GO

-- City-Town relationship
CREATE INDEX IX_CompanyUsers_CompanyID_CityID_TownID 
ON CompanyUsers (CompanyID, CityID, TownID)
GO

-- Covering index
CREATE INDEX IX_CompanyUsers_CompanyID_IsActive 
ON CompanyUsers (CompanyID, IsActive)
INCLUDE (CompanyUserID, Name, Email, PhoneNumber, CityID, TownID, CreationDate, UpdatedDate)
GO

-- Full-text search
CREATE FULLTEXT INDEX FTI_CompanyUsers_SearchFields
ON CompanyUsers (Name, Email, PhoneNumber)
GO

-- =====================================================
-- 9. USERS TABLE (HIGH PRIORITY - Authentication)
-- =====================================================
PRINT 'Creating User indexes...'

-- Email lookup (ULTRA KRİTİK - Authentication)
CREATE UNIQUE INDEX IX_Users_Email 
ON Users (Email)
WHERE Email IS NOT NULL
GO

-- Active users
CREATE INDEX IX_Users_IsActive_UserID 
ON Users (IsActive, UserID)
GO

-- Name search
CREATE INDEX IX_Users_IsActive_FirstName_LastName 
ON Users (IsActive, FirstName, LastName)
GO

-- Covering index
CREATE INDEX IX_Users_IsActive_Email 
ON Users (IsActive, Email)
INCLUDE (UserID, FirstName, LastName, CreationDate, UpdatedDate, ProfileImagePath)
GO

-- =====================================================
-- 10. OPERATION CLAIMS TABLE (HIGH PRIORITY - Authorization)
-- =====================================================
PRINT 'Creating OperationClaim indexes...'

-- Name lookup (ULTRA KRİTİK - Authorization)
CREATE UNIQUE INDEX IX_OperationClaims_Name 
ON OperationClaims (Name)
WHERE Name IS NOT NULL
GO

-- Active claims
CREATE INDEX IX_OperationClaims_IsActive_OperationClaimId 
ON OperationClaims (IsActive, OperationClaimId)
GO

-- Covering index
CREATE INDEX IX_OperationClaims_OperationClaimId_IsActive 
ON OperationClaims (OperationClaimId, IsActive)
INCLUDE (Name, CreationDate, UpdatedDate)
GO

-- =====================================================
-- 11. EXPENSES TABLE (HIGH PRIORITY - Financial)
-- =====================================================
PRINT 'Creating Expense indexes...'

-- Multi-tenant + Date range (Dashboard)
CREATE INDEX IX_Expenses_CompanyID_IsActive_ExpenseDate 
ON Expenses (CompanyID, IsActive, ExpenseDate)
GO

-- Expense type filtering
CREATE INDEX IX_Expenses_CompanyID_IsActive_ExpenseType 
ON Expenses (CompanyID, IsActive, ExpenseType)
WHERE ExpenseType IS NOT NULL
GO

-- Amount range queries
CREATE INDEX IX_Expenses_CompanyID_IsActive_Amount 
ON Expenses (CompanyID, IsActive, Amount)
GO

-- Monthly grouping optimization
CREATE INDEX IX_Expenses_CompanyID_ExpenseDate_Year_Month 
ON Expenses (CompanyID, ExpenseDate)
WHERE IsActive = 1
INCLUDE (Amount, ExpenseType)
GO

-- Covering index
CREATE INDEX IX_Expenses_CompanyID_IsActive_ExpenseDate_DESC 
ON Expenses (CompanyID, IsActive, ExpenseDate DESC)
INCLUDE (ExpenseID, Description, Amount, ExpenseType, CreationDate)
GO

-- Full-text search
CREATE FULLTEXT INDEX FTI_Expenses_SearchFields
ON Expenses (Description, ExpenseType)
GO

-- =====================================================
-- 12. LICENSE TRANSACTIONS TABLE (HIGH PRIORITY - Financial)
-- =====================================================
PRINT 'Creating LicenseTransaction indexes...'

-- Active transactions
CREATE INDEX IX_LicenseTransactions_IsActive_TransactionDate 
ON LicenseTransactions (IsActive, TransactionDate)
GO

-- User transaction history
CREATE INDEX IX_LicenseTransactions_UserID_IsActive_TransactionDate 
ON LicenseTransactions (UserID, IsActive, TransactionDate)
GO

-- Payment method analysis
CREATE INDEX IX_LicenseTransactions_IsActive_PaymentMethod 
ON LicenseTransactions (IsActive, PaymentMethod)
GO

-- Monthly revenue optimization
CREATE INDEX IX_LicenseTransactions_TransactionDate_Year_Month 
ON LicenseTransactions (TransactionDate)
WHERE IsActive = 1
INCLUDE (Amount, PaymentMethod)
GO

-- Covering index
CREATE INDEX IX_LicenseTransactions_IsActive_TransactionDate_PaymentMethod 
ON LicenseTransactions (IsActive, TransactionDate, PaymentMethod)
INCLUDE (LicenseTransactionID, UserID, LicensePackageID, UserLicenseID, Amount, CreationDate)
GO

-- =====================================================
-- 13. MEMBERSHIP TYPES TABLE (HIGH PRIORITY - Business Logic)
-- =====================================================
PRINT 'Creating MembershipType indexes...'

-- Multi-tenant base query
CREATE INDEX IX_MembershipTypes_CompanyID_IsActive_MembershipTypeID 
ON MembershipTypes (CompanyID, IsActive, MembershipTypeID)
GO

-- Branch filtering
CREATE INDEX IX_MembershipTypes_CompanyID_Branch_IsActive 
ON MembershipTypes (CompanyID, Branch, IsActive)
GO

-- Single lookup optimization
CREATE INDEX IX_MembershipTypes_MembershipTypeID_CompanyID 
ON MembershipTypes (MembershipTypeID, CompanyID)
GO

-- Covering index
CREATE INDEX IX_MembershipTypes_CompanyID_MembershipTypeID_IsActive 
ON MembershipTypes (CompanyID, MembershipTypeID, IsActive)
INCLUDE (Branch, TypeName, Day, Price, CreationDate, UpdatedDate)
GO

-- =====================================================
-- 14. REMAINING DEBTS TABLE (HIGH PRIORITY - Financial)
-- =====================================================
PRINT 'Creating RemainingDebt indexes...'

-- Multi-tenant + Payment relationship
CREATE INDEX IX_RemainingDebts_CompanyID_PaymentID_IsActive 
ON RemainingDebts (CompanyID, PaymentID, IsActive)
GO

-- Active debts query (ULTRA KRİTİK)
CREATE INDEX IX_RemainingDebts_CompanyID_IsActive_RemainingAmount 
ON RemainingDebts (CompanyID, IsActive, RemainingAmount)
GO

-- Payment lookup
CREATE INDEX IX_RemainingDebts_PaymentID_CompanyID 
ON RemainingDebts (PaymentID, CompanyID)
GO

-- Covering index
CREATE INDEX IX_RemainingDebts_CompanyID_PaymentID 
ON RemainingDebts (CompanyID, PaymentID)
INCLUDE (RemainingDebtID, OriginalAmount, RemainingAmount, LastUpdateDate, IsActive, CreationDate)
GO

-- =====================================================
-- 15. ENTRY EXIT HISTORY TABLE (HIGH PRIORITY - Real-time)
-- =====================================================
PRINT 'Creating EntryExitHistory indexes...'

-- Multi-tenant + Membership relationship
CREATE INDEX IX_EntryExitHistory_CompanyID_MembershipID_IsActive 
ON EntryExitHistory (CompanyID, MembershipID, IsActive)
GO

-- Date range queries (Reports)
CREATE INDEX IX_EntryExitHistory_CompanyID_IsActive_EntryDate 
ON EntryExitHistory (CompanyID, IsActive, EntryDate)
GO

-- Today's entries (Real-time)
CREATE INDEX IX_EntryExitHistory_CompanyID_EntryDate_IsActive 
ON EntryExitHistory (CompanyID, EntryDate, IsActive)
GO

-- Member entry history
CREATE INDEX IX_EntryExitHistory_MembershipID_CompanyID_EntryDate 
ON EntryExitHistory (MembershipID, CompanyID, EntryDate)
GO

-- Covering index
CREATE INDEX IX_EntryExitHistory_CompanyID_MembershipID_EntryDate 
ON EntryExitHistory (CompanyID, MembershipID, EntryDate)
INCLUDE (EntryExitHistoryID, ExitDate, IsActive, CreationDate)
GO

PRINT 'HIGH PRIORITY indexes completed successfully!'
PRINT 'Total estimated RAM usage so far: ~2.3 GB'
GO
