-- =====================================================
-- GYM PROJECT - DATABASE INDEX CREATION SCRIPT
-- PART 3: MEDIUM PRIORITY INDEXES
-- =====================================================
-- Bu script business operations ve modül-specific indexleri oluşturur
-- Estimated RAM Usage: ~800 MB additional
-- =====================================================

USE [GymProjectDatabase]
GO

PRINT 'Starting MEDIUM PRIORITY index creation...'
PRINT 'Estimated completion time: 15-25 minutes'
GO

-- =====================================================
-- 16. PRODUCTS TABLE (MEDIUM PRIORITY - Transaction Support)
-- =====================================================
PRINT 'Creating Product indexes...'

-- Multi-tenant base query
CREATE INDEX IX_Products_CompanyID_IsActive_ProductID 
ON Products (CompanyID, IsActive, ProductID)
GO

-- Name search
CREATE INDEX IX_Products_CompanyID_IsActive_Name 
ON Products (CompanyID, IsActive, Name)
GO

-- Price range queries
CREATE INDEX IX_Products_CompanyID_IsActive_Price 
ON Products (CompanyID, IsActive, Price)
GO

-- Single product lookup
CREATE INDEX IX_Products_ProductID_CompanyID 
ON Products (ProductID, CompanyID)
GO

-- Covering index
CREATE INDEX IX_Products_CompanyID_IsActive 
ON Products (CompanyID, IsActive)
INCLUDE (ProductID, Name, Price, CreationDate, UpdatedDate)
GO

-- =====================================================
-- 17. WORKOUT PROGRAM TEMPLATES TABLE (MEDIUM PRIORITY - Workout Module)
-- =====================================================
PRINT 'Creating WorkoutProgramTemplate indexes...'

-- Multi-tenant base query
CREATE INDEX IX_WorkoutProgramTemplates_CompanyID_IsActive_CreationDate 
ON WorkoutProgramTemplates (CompanyID, IsActive, CreationDate)
GO

-- Program name uniqueness (Business validation)
CREATE UNIQUE INDEX IX_WorkoutProgramTemplates_CompanyID_ProgramName_IsActive 
ON WorkoutProgramTemplates (CompanyID, ProgramName, IsActive)
WHERE IsActive = 1
GO

-- Experience level filtering
CREATE INDEX IX_WorkoutProgramTemplates_CompanyID_IsActive_ExperienceLevel 
ON WorkoutProgramTemplates (CompanyID, IsActive, ExperienceLevel)
WHERE ExperienceLevel IS NOT NULL
GO

-- Single template lookup
CREATE INDEX IX_WorkoutProgramTemplates_WorkoutProgramTemplateID_CompanyID 
ON WorkoutProgramTemplates (WorkoutProgramTemplateID, CompanyID)
GO

-- Covering index
CREATE INDEX IX_WorkoutProgramTemplates_CompanyID_IsActive 
ON WorkoutProgramTemplates (CompanyID, IsActive)
INCLUDE (WorkoutProgramTemplateID, ProgramName, Description, ExperienceLevel, TargetGoal, CreationDate, UpdatedDate)
GO

-- =====================================================
-- 18. WORKOUT PROGRAM DAYS TABLE (MEDIUM PRIORITY - Workout Module)
-- =====================================================
PRINT 'Creating WorkoutProgramDay indexes...'

-- Template relationship + sorting (ULTRA KRİTİK)
CREATE INDEX IX_WorkoutProgramDays_WorkoutProgramTemplateID_DayNumber 
ON WorkoutProgramDays (WorkoutProgramTemplateID, DayNumber)
GO

-- Multi-tenant filtering
CREATE INDEX IX_WorkoutProgramDays_CompanyID_WorkoutProgramTemplateID 
ON WorkoutProgramDays (CompanyID, WorkoutProgramTemplateID)
GO

-- Covering index
CREATE INDEX IX_WorkoutProgramDays_WorkoutProgramTemplateID 
ON WorkoutProgramDays (WorkoutProgramTemplateID)
INCLUDE (WorkoutProgramDayID, CompanyID, DayNumber, DayName, IsRestDay, CreationDate)
GO

-- =====================================================
-- 19. WORKOUT PROGRAM EXERCISES TABLE (MEDIUM PRIORITY - Workout Module)
-- =====================================================
PRINT 'Creating WorkoutProgramExercise indexes...'

-- Day relationship + ordering (ULTRA KRİTİK)
CREATE INDEX IX_WorkoutProgramExercises_WorkoutProgramDayID_OrderIndex 
ON WorkoutProgramExercises (WorkoutProgramDayID, OrderIndex)
GO

-- Multi-tenant filtering
CREATE INDEX IX_WorkoutProgramExercises_CompanyID_WorkoutProgramDayID 
ON WorkoutProgramExercises (CompanyID, WorkoutProgramDayID)
GO

-- Exercise type polymorphism
CREATE INDEX IX_WorkoutProgramExercises_ExerciseType_ExerciseID 
ON WorkoutProgramExercises (ExerciseType, ExerciseID)
GO

-- Covering index
CREATE INDEX IX_WorkoutProgramExercises_WorkoutProgramDayID 
ON WorkoutProgramExercises (WorkoutProgramDayID)
INCLUDE (WorkoutProgramExerciseID, CompanyID, ExerciseType, ExerciseID, OrderIndex, Sets, Reps, RestTime, Notes, CreationDate)
GO

-- =====================================================
-- 20. SYSTEM EXERCISES TABLE (MEDIUM PRIORITY - Exercise Library)
-- =====================================================
PRINT 'Creating SystemExercise indexes...'

-- Category JOIN (ULTRA KRİTİK)
CREATE INDEX IX_SystemExercises_ExerciseCategoryID_IsActive 
ON SystemExercises (ExerciseCategoryID, IsActive)
GO

-- Active exercises
CREATE INDEX IX_SystemExercises_IsActive_ExerciseName 
ON SystemExercises (IsActive, ExerciseName)
GO

-- Difficulty level filtering
CREATE INDEX IX_SystemExercises_IsActive_DifficultyLevel 
ON SystemExercises (IsActive, DifficultyLevel)
WHERE DifficultyLevel IS NOT NULL
GO

-- Covering index
CREATE INDEX IX_SystemExercises_IsActive_ExerciseCategoryID 
ON SystemExercises (IsActive, ExerciseCategoryID)
INCLUDE (SystemExerciseID, ExerciseName, Description, Instructions, MuscleGroups, Equipment, DifficultyLevel, CreationDate)
GO

-- Full-text search
CREATE FULLTEXT INDEX FTI_SystemExercises_SearchFields
ON SystemExercises (ExerciseName, Description, MuscleGroups)
GO

-- =====================================================
-- 21. COMPANY EXERCISES TABLE (MEDIUM PRIORITY - Exercise Library)
-- =====================================================
PRINT 'Creating CompanyExercise indexes...'

-- Multi-tenant + Category JOIN (ULTRA KRİTİK)
CREATE INDEX IX_CompanyExercises_CompanyID_ExerciseCategoryID_IsActive 
ON CompanyExercises (CompanyID, ExerciseCategoryID, IsActive)
GO

-- Company exercises
CREATE INDEX IX_CompanyExercises_CompanyID_IsActive_ExerciseName 
ON CompanyExercises (CompanyID, IsActive, ExerciseName)
GO

-- Difficulty level filtering
CREATE INDEX IX_CompanyExercises_CompanyID_IsActive_DifficultyLevel 
ON CompanyExercises (CompanyID, IsActive, DifficultyLevel)
WHERE DifficultyLevel IS NOT NULL
GO

-- Covering index
CREATE INDEX IX_CompanyExercises_CompanyID_IsActive 
ON CompanyExercises (CompanyID, IsActive)
INCLUDE (CompanyExerciseID, ExerciseCategoryID, ExerciseName, Description, Instructions, MuscleGroups, Equipment, DifficultyLevel, CreationDate)
GO

-- Full-text search
CREATE FULLTEXT INDEX FTI_CompanyExercises_SearchFields
ON CompanyExercises (ExerciseName, Description, MuscleGroups)
GO

-- =====================================================
-- 22. MEMBER WORKOUT PROGRAMS TABLE (MEDIUM PRIORITY - Workout Module)
-- =====================================================
PRINT 'Creating MemberWorkoutProgram indexes...'

-- Multi-tenant + Member relationship
CREATE INDEX IX_MemberWorkoutPrograms_CompanyID_MemberID_IsActive 
ON MemberWorkoutPrograms (CompanyID, MemberID, IsActive)
GO

-- Template assignments
CREATE INDEX IX_MemberWorkoutPrograms_CompanyID_WorkoutProgramTemplateID_IsActive 
ON MemberWorkoutPrograms (CompanyID, WorkoutProgramTemplateID, IsActive)
GO

-- Active programs (Date range)
CREATE INDEX IX_MemberWorkoutPrograms_CompanyID_IsActive_StartDate_EndDate 
ON MemberWorkoutPrograms (CompanyID, IsActive, StartDate, EndDate)
GO

-- Covering index
CREATE INDEX IX_MemberWorkoutPrograms_CompanyID_MemberID_WorkoutProgramTemplateID 
ON MemberWorkoutPrograms (CompanyID, MemberID, WorkoutProgramTemplateID)
INCLUDE (MemberWorkoutProgramID, StartDate, EndDate, Notes, IsActive, CreationDate, UpdatedDate)
GO

-- =====================================================
-- 23. MEMBERSHIP FREEZE HISTORY TABLE (MEDIUM PRIORITY - Business Logic)
-- =====================================================
PRINT 'Creating MembershipFreezeHistory indexes...'

-- Multi-tenant + Membership relationship
CREATE INDEX IX_MembershipFreezeHistory_CompanyID_MembershipID_CreationDate 
ON MembershipFreezeHistory (CompanyID, MembershipID, CreationDate)
GO

-- Yearly calculation optimization
CREATE INDEX IX_MembershipFreezeHistory_MembershipID_CreationDate_CompanyID 
ON MembershipFreezeHistory (MembershipID, CreationDate, CompanyID)
GO

-- Date range queries
CREATE INDEX IX_MembershipFreezeHistory_CompanyID_StartDate_PlannedEndDate 
ON MembershipFreezeHistory (CompanyID, StartDate, PlannedEndDate)
GO

-- Covering index
CREATE INDEX IX_MembershipFreezeHistory_CompanyID_MembershipID 
ON MembershipFreezeHistory (CompanyID, MembershipID)
INCLUDE (FreezeHistoryID, StartDate, PlannedEndDate, ActualEndDate, FreezeDays, UsedDays, CancellationType, CreationDate)
GO

-- =====================================================
-- 24. USER LICENSES TABLE (MEDIUM PRIORITY - License Management)
-- =====================================================
PRINT 'Creating UserLicense indexes...'

-- User license relationship (ULTRA KRİTİK)
CREATE INDEX IX_UserLicenses_UserID_IsActive_EndDate 
ON UserLicenses (UserID, IsActive, EndDate)
GO

-- Active license check
CREATE INDEX IX_UserLicenses_IsActive_EndDate 
ON UserLicenses (IsActive, EndDate)
GO

-- License package relationship
CREATE INDEX IX_UserLicenses_LicensePackageID_IsActive 
ON UserLicenses (LicensePackageID, IsActive)
GO

-- Covering index
CREATE INDEX IX_UserLicenses_UserID_LicensePackageID 
ON UserLicenses (UserID, LicensePackageID)
INCLUDE (UserLicenseID, StartDate, EndDate, IsActive, CreationDate, UpdatedDate)
GO

-- =====================================================
-- 25. USER COMPANIES TABLE (MEDIUM PRIORITY - Bridge Table)
-- =====================================================
PRINT 'Creating UserCompany indexes...'

-- User-Company relationship
CREATE INDEX IX_UserCompanies_UserID_IsActive 
ON UserCompanies (UserID, IsActive)
GO

-- Company users lookup
CREATE INDEX IX_UserCompanies_CompanyId_IsActive 
ON UserCompanies (CompanyId, IsActive)
GO

-- User-Company unique relationship
CREATE UNIQUE INDEX IX_UserCompanies_UserID_CompanyId_IsActive 
ON UserCompanies (UserID, CompanyId, IsActive)
WHERE IsActive = 1
GO

-- Covering index
CREATE INDEX IX_UserCompanies_UserID_CompanyId 
ON UserCompanies (UserID, CompanyId)
INCLUDE (UserCompanyID, IsActive, CreationDate, UpdatedDate)
GO

PRINT 'MEDIUM PRIORITY indexes completed successfully!'
PRINT 'Total estimated RAM usage so far: ~3.1 GB'
GO
