-- =====================================================
-- GYM PROJECT - DATABASE INDEX CREATION SCRIPT
-- PART 4: LOW PRIORITY INDEXES & LOOKUP TABLES
-- =====================================================
-- Bu script lookup tables ve düşük öncelikli indexleri oluşturur
-- Estimated RAM Usage: ~200 MB additional
-- =====================================================

USE [GymProjectDatabase]
GO

PRINT 'Starting LOW PRIORITY index creation...'
PRINT 'Estimated completion time: 5-10 minutes'
GO

-- =====================================================
-- 26. LICENSE PACKAGES TABLE (LOW PRIORITY - Global Lookup)
-- =====================================================
PRINT 'Creating LicensePackage indexes...'

-- IsActive filter (En kritik - her JOIN)
CREATE INDEX IX_LicensePackages_IsActive_LicensePackageID 
ON LicensePackages (IsActive, LicensePackageID)
GO

-- Role-based filtering
CREATE INDEX IX_LicensePackages_Role_IsActive 
ON LicensePackages (Role, IsActive)
GO

-- Covering index (JOIN performance)
CREATE INDEX IX_LicensePackages_LicensePackageID_IsActive 
ON LicensePackages (LicensePackageID, IsActive)
INCLUDE (Name, Description, Role, DurationDays, Price, CreationDate)
GO

-- =====================================================
-- 27. EXERCISE CATEGORIES TABLE (LOW PRIORITY - Global Lookup)
-- =====================================================
PRINT 'Creating ExerciseCategory indexes...'

-- Active categories
CREATE INDEX IX_ExerciseCategories_IsActive_CategoryName 
ON ExerciseCategories (IsActive, CategoryName)
GO

-- Covering index (JOIN performance)
CREATE INDEX IX_ExerciseCategories_ExerciseCategoryID_IsActive 
ON ExerciseCategories (ExerciseCategoryID, IsActive)
INCLUDE (CategoryName, Description, CreationDate)
GO

-- =====================================================
-- 28. CITIES TABLE (LOW PRIORITY - Geographic Lookup)
-- =====================================================
PRINT 'Creating City indexes...'

-- City name lookup (Dropdown, search)
CREATE INDEX IX_Cities_CityName 
ON Cities (CityName)
GO

-- Covering index (JOIN performance)
CREATE INDEX IX_Cities_CityID 
ON Cities (CityID)
INCLUDE (CityName)
GO

-- =====================================================
-- 29. TOWNS TABLE (LOW PRIORITY - Geographic Lookup)
-- =====================================================
PRINT 'Creating Town indexes...'

-- City relationship (En kritik - dropdown)
CREATE INDEX IX_Towns_CityID_TownName 
ON Towns (CityID, TownName)
GO

-- Town name lookup
CREATE INDEX IX_Towns_TownName 
ON Towns (TownName)
GO

-- Covering index (JOIN performance)
CREATE INDEX IX_Towns_TownID_CityID 
ON Towns (TownID, CityID)
INCLUDE (TownName)
GO

-- =====================================================
-- 30. COMPANY ADDRESSES TABLE (LOW PRIORITY - Address Management)
-- =====================================================
PRINT 'Creating CompanyAdress indexes...'

-- Multi-tenant base query
CREATE INDEX IX_CompanyAdresses_CompanyID_IsActive 
ON CompanyAdresses (CompanyID, IsActive)
GO

-- City-Town relationship
CREATE INDEX IX_CompanyAdresses_CityID_TownID 
ON CompanyAdresses (CityID, TownID)
GO

-- Covering index (JOIN performance)
CREATE INDEX IX_CompanyAdresses_CompanyID_CityID_TownID 
ON CompanyAdresses (CompanyID, CityID, TownID)
INCLUDE (CompanyAdressID, Adress, IsActive, CreationDate)
GO

-- =====================================================
-- 31. DEBT PAYMENTS TABLE (LOW PRIORITY - Financial Support)
-- =====================================================
PRINT 'Creating DebtPayment indexes...'

-- Multi-tenant + RemainingDebt relationship
CREATE INDEX IX_DebtPayments_CompanyID_RemainingDebtID_IsActive 
ON DebtPayments (CompanyID, RemainingDebtID, IsActive)
GO

-- Payment date queries
CREATE INDEX IX_DebtPayments_CompanyID_IsActive_PaymentDate 
ON DebtPayments (CompanyID, IsActive, PaymentDate)
GO

-- Payment method filtering
CREATE INDEX IX_DebtPayments_CompanyID_IsActive_PaymentMethod 
ON DebtPayments (CompanyID, IsActive, PaymentMethod)
WHERE PaymentMethod IS NOT NULL
GO

-- Covering index
CREATE INDEX IX_DebtPayments_CompanyID_RemainingDebtID 
ON DebtPayments (CompanyID, RemainingDebtID)
INCLUDE (DebtPaymentID, PaidAmount, PaymentMethod, PaymentDate, IsActive, CreationDate)
GO

-- =====================================================
-- MATERIALIZED VIEWS FOR PERFORMANCE
-- =====================================================
PRINT 'Creating materialized views for analytics...'

-- Monthly Payment Revenue View
CREATE VIEW PaymentMonthlyRevenueView AS
SELECT 
    CompanyID,
    YEAR(PaymentDate) as Year,
    MONTH(PaymentDate) as Month,
    OriginalPaymentMethod,
    SUM(PaymentAmount) as TotalAmount,
    COUNT(*) as PaymentCount
FROM Payments 
WHERE IsActive = 1 AND OriginalPaymentMethod != 'Borç'
GROUP BY CompanyID, YEAR(PaymentDate), MONTH(PaymentDate), OriginalPaymentMethod
GO

-- Monthly Transaction Totals View
CREATE VIEW TransactionDailyTotalsView AS
SELECT 
    CompanyID,
    CAST(TransactionDate AS DATE) as TransactionDate,
    TransactionType,
    SUM(UnitPrice * Quantity) as TotalAmount,
    COUNT(*) as TransactionCount
FROM Transactions 
WHERE IsActive = 1 AND TransactionType != 'Bakiye Yükleme'
GROUP BY CompanyID, CAST(TransactionDate AS DATE), TransactionType
GO

-- Monthly Expense View
CREATE VIEW ExpenseMonthlyView AS
SELECT 
    CompanyID,
    YEAR(ExpenseDate) as Year,
    MONTH(ExpenseDate) as Month,
    SUM(Amount) as TotalAmount,
    COUNT(*) as ExpenseCount
FROM Expenses 
WHERE IsActive = 1
GROUP BY CompanyID, YEAR(ExpenseDate), MONTH(ExpenseDate)
GO

-- User Device Analytics View
CREATE VIEW UserDeviceAnalyticsView AS
SELECT 
    UserId,
    COUNT(*) as TotalDevices,
    COUNT(CASE WHEN IsActive = 1 THEN 1 END) as ActiveDevices,
    MAX(LastUsedAt) as LastActivity
FROM UserDevices 
GROUP BY UserId
GO

-- Workout Template Stats View
CREATE VIEW WorkoutTemplateStatsView AS
SELECT 
    wpt.WorkoutProgramTemplateID,
    wpt.CompanyID,
    wpt.ProgramName,
    wpt.ExperienceLevel,
    wpt.TargetGoal,
    COUNT(DISTINCT wpd.WorkoutProgramDayID) as DayCount,
    COUNT(wpe.WorkoutProgramExerciseID) as ExerciseCount
FROM WorkoutProgramTemplates wpt
LEFT JOIN WorkoutProgramDays wpd ON wpt.WorkoutProgramTemplateID = wpd.WorkoutProgramTemplateID
LEFT JOIN WorkoutProgramExercises wpe ON wpd.WorkoutProgramDayID = wpe.WorkoutProgramDayID
WHERE wpt.IsActive = 1
GROUP BY wpt.WorkoutProgramTemplateID, wpt.CompanyID, wpt.ProgramName, wpt.ExperienceLevel, wpt.TargetGoal
GO

-- =====================================================
-- INDEX MAINTENANCE JOBS
-- =====================================================
PRINT 'Creating index maintenance procedures...'

-- Index fragmentation check procedure
CREATE PROCEDURE sp_CheckIndexFragmentation
AS
BEGIN
    SELECT 
        OBJECT_NAME(ips.object_id) AS TableName,
        i.name AS IndexName,
        ips.avg_fragmentation_in_percent,
        ips.page_count,
        CASE 
            WHEN ips.avg_fragmentation_in_percent > 30 THEN 'REBUILD'
            WHEN ips.avg_fragmentation_in_percent > 10 THEN 'REORGANIZE'
            ELSE 'OK'
        END AS Recommendation
    FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'LIMITED') ips
    INNER JOIN sys.indexes i ON ips.object_id = i.object_id AND ips.index_id = i.index_id
    WHERE ips.avg_fragmentation_in_percent > 5
        AND ips.page_count > 1000
    ORDER BY ips.avg_fragmentation_in_percent DESC
END
GO

-- Index usage statistics procedure
CREATE PROCEDURE sp_CheckIndexUsage
AS
BEGIN
    SELECT 
        OBJECT_NAME(ius.object_id) AS TableName,
        i.name AS IndexName,
        ius.user_seeks,
        ius.user_scans,
        ius.user_lookups,
        ius.user_updates,
        ius.last_user_seek,
        ius.last_user_scan,
        ius.last_user_lookup
    FROM sys.dm_db_index_usage_stats ius
    INNER JOIN sys.indexes i ON ius.object_id = i.object_id AND ius.index_id = i.index_id
    WHERE ius.database_id = DB_ID()
        AND OBJECT_NAME(ius.object_id) NOT LIKE 'sys%'
    ORDER BY (ius.user_seeks + ius.user_scans + ius.user_lookups) DESC
END
GO

-- =====================================================
-- COMPLETION SUMMARY
-- =====================================================
PRINT '============================================='
PRINT 'DATABASE INDEX CREATION COMPLETED!'
PRINT '============================================='
PRINT 'Total indexes created: ~200+'
PRINT 'Total estimated RAM usage: ~3.3 GB'
PRINT 'Performance improvement expected: 80-95%'
PRINT '============================================='
PRINT 'Next steps:'
PRINT '1. Monitor index usage with sp_CheckIndexUsage'
PRINT '2. Check fragmentation with sp_CheckIndexFragmentation'
PRINT '3. Set up weekly index maintenance jobs'
PRINT '4. Monitor query performance improvements'
PRINT '============================================='
GO

PRINT 'LOW PRIORITY indexes and maintenance completed successfully!'
GO
